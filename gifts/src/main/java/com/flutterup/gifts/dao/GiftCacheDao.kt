package com.flutterup.gifts.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.flutterup.gifts.entity.GiftCacheEntity

@Dao
interface GiftCacheDao {

    @Query("SELECT * FROM gift_cache WHERE giftId = :giftId")
    suspend fun getGift(giftId: String): GiftCacheEntity?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertGift(gift: GiftCacheEntity)

    @Query("DELETE FROM gift_cache WHERE giftId = :giftId")
    suspend fun deleteGift(giftId: String)
}